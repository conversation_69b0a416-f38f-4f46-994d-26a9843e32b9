<template>
	<view class="record-compare-page">
		<!-- 顶部书本信息卡片 -->
		<view class="book-header-card" :style="{ 'background-image': 'url(' + (pageData.type == 1 ? '/static/images/record/bg1.png' : '/static/images/record/bg2.png') + ')' }">
			<view class="book-cover">
				<image :src="bookData.thumb" mode="aspectFit"></image>
			</view>
			<view class="book-info">
				<view class="book-title">{{ bookData.name }}</view>
				<view class="book-subtitle">英语同步练</view>
				<view class="grade-tag">
					<text>切换年级</text>
				</view>
			</view>
		</view>

		<!-- 单元列表 -->
		<view class="units-container">
			<view 
				class="unit-item" 
				v-for="(unit, index) in bookData.unit_lists" 
				:key="index"
				:class="{ expanded: expandedUnits.includes(index) }" 
				@click="unitItemClick(index)"
			>
				<view class="unit-header" @click.stop="toggleUnit(index)">
					<view class="unit-title">
						<text class="unit-name">{{ unit.name }}</text>
						<text class="unit-subtitle">{{ unit.desc }}</text>
					</view>
					<view class="unit-icon">
						<uni-icons v-if="pageData.vip == 0 && index >= 1" type="locked-filled" size="16" color="#ccc"></uni-icons>
						<uni-icons v-else :type="expandedUnits.includes(index) ? 'down' : 'right'" size="16" color="#999"></uni-icons>
					</view>
				</view>
				<view class="unit-content" v-show="expandedUnits.includes(index) && (pageData.vip != 0 || index < 1)">
					<view 
						class="lesson-item" 
						v-for="(lesson, lessonIndex) in unit.course" 
						:key="lessonIndex"
						:class="{ active: selectedLesson.unit === index && selectedLesson.lesson === lessonIndex }"
						@click.stop="selectLesson(lesson)"
					>
						<view class="lesson-dot" :class="{ 'active-dot': selectedLesson.unit === index && selectedLesson.lesson === lessonIndex }"></view>
						<text class="lesson-text" :class="{ 'active-text': selectedLesson.unit === index && selectedLesson.lesson === lessonIndex }">{{ lesson.lesson }}</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import http from '/api/index.js';
import Tools from '/utils/index.js';

export default {
	data() {
		return {
			expandedUnits: [0], // 默认展开第一个单元
			selectedLesson: { unit: 0, lesson: 0 },
			pageData: '',
      bookData: ''
		};
	},
	onLoad(data) {
		this.pageData = data;
         this.pageData.vip  = 1;


		this.getSentencenUits();
	},
	methods: {
		getSentencenUits() {
			const data = {
				userkey: this.pageData.userkey,
				book_id: this.pageData.book_id
			};
			http.getSentencenUits(data).then((res) => {
        this.bookData = res.data.data;
				console.log(res);
			});
		},
		updateVip() {
			this.pageData.vip = 1;
		},
		toggleUnit(index) {
			// 如果不是VIP且是第二单元及以后，不允许展开
			if (this.pageData.vip == 0 && index >= 1) {
				console.log('需要开通VIP才能访问');
				return;
			}

			// 关闭其他所有单元，只展开当前单元
			if (this.expandedUnits.includes(index)) {
				this.expandedUnits = [];
			} else {
				this.expandedUnits = [index];
			}
		},
		selectLesson(lesson) {

		   Tools.setStorage('recordCompare', lesson);
			
			// 跳转页面逻辑
			uni.navigateTo({
				url: `/pages/book/repeatRead`
			});
		},
		unitItemClick(index) {
			// 如果不是VIP且是第二单元及以后，不允许点击
			if (this.pageData.vip == 0 && index >= 1) {
				console.log('需要开通VIP才能访问');
				return;
			}

			this.toggleUnit(index);
		}
	}
};
</script>

<style lang="scss" scoped>
.record-compare-page {
	min-height: 100vh;
	padding: 0;
}

.book-header-card {
	padding: 40rpx 30rpx;
	display: flex;
	align-items: center;
	position: relative;
	overflow: hidden;
	width: 100%;
	margin: 0;
	border-radius: 0;
	background-image: url('/static/images/record/bg1.png');
	background-size: cover;
	background-position: center;
	background-repeat: no-repeat;
}

.book-cover {
	width: 120rpx;
	height: 160rpx;
	background: #fff;
	border-radius: 12rpx;
	margin-right: 30rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);

	image {
		width: 100%;
		height: 100%;
		border-radius: 12rpx;
	}
}

.book-info {
	flex: 1;
	position: relative;
	z-index: 1;
}

.book-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 8rpx;
}

.book-subtitle {
	font-size: 28rpx;
	color: #666;
	margin-bottom: 20rpx;
}

.grade-tag {
	background: #4a90e2;
	color: white;
	padding: 8rpx 20rpx;
	border-radius: 20rpx;
	font-size: 24rpx;
	display: inline-block;
}

.units-container {
	padding: 0 20rpx;
	margin-top: 20rpx;
}

.unit-item {
	background: white;
	border-radius: 16rpx;
	margin-bottom: 16rpx;
	overflow: hidden;
	border: 1px solid #dfdfdf;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
	cursor: pointer;
	transition: all 0.3s ease;

	&:hover {
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
	}

	&.expanded {
		border: 1px solid #FFE986 !important;
	}

	.unit-header {
		padding: 24rpx 30rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;

		.unit-title {
			.unit-name {
				font-size: 30rpx;
				font-weight: bold;
				color: #333;
				margin-bottom: 4rpx;
				display: block;
			}

			.unit-subtitle {
				font-size: 26rpx;
				color: #999;
			}
		}

		.unit-icon {
			opacity: 0.6;
			transition: transform 0.3s ease;
		}
	}

	&.expanded .unit-header {
		border-bottom: 1rpx solid #f0f0f0;

		.unit-icon {
			transform: rotate(0deg);
		}
	}

	.unit-content {
		padding: 16rpx 30rpx 24rpx;
		transition: all 0.3s ease;

		.lesson-item {
			display: flex;
			align-items: center;
			padding: 16rpx 0;
			cursor: pointer;

			.lesson-dot {
				width: 10rpx;
				height: 10rpx;
				border-radius: 50%;
				background: #ddd;
				margin-right: 20rpx;

				&.active-dot {
					background: #ffb800;
				}
			}

			.lesson-text {
				font-size: 26rpx;
				color: #666;

				&.active-text {
					color: #ffb800;
					font-weight: 500;
				}
			}

			&.active {
				.lesson-text {
					color: #ffb800;
					font-weight: 500;
				}
			}
		}
	}
}
</style>






