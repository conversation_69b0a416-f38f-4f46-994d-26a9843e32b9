<template>
  <view class="page-container">
    <!-- 句子列表 -->
    <view class="sentence-list">
      <view 
        v-for="(item, index) in sentences" 
        :key="index"
        class="sentence-card"
        :class="{ 'highlighted': item.highlighted, 'recording': item.recording }"
        @click="selectSentence(index)"
      >
        <view class="sentence-content">
          <text class="sentence-text" :style="{ color: item.textColor }">
            {{ item.text }}
          </text>
          <!-- 录音按钮 (所有句子录完后显示) -->
<view v-if="item.showButtons && allSentencesRecorded" class="button-group">
            <view
              class="record-btn original"
              :class="{ 'playing': isPlaying && currentPlayingIndex === index && currentPlayingType === 'original' }"
              @click.stop="playOriginal(index)"
            >
		    	<uni-icons type="sound-filled" color="#fff" ></uni-icons>
              <text class="btn-text">
                {{ isPlaying && currentPlayingIndex === index && currentPlayingType === 'original' ? '播放中...' : '播放原音' }}
              </text>
            </view>
            <view
              class="record-btn recorded"
              :class="{ 'playing': isPlaying && currentPlayingIndex === index && currentPlayingType === 'recorded' }"
              @click.stop="playRecorded(index)"
            >
              <uni-icons type="mic-filled" color="#fff" ></uni-icons>
              <text class="btn-text">
                {{ isPlaying && currentPlayingIndex === index && currentPlayingType === 'recorded' ? '播放中...' : '播放录音' }}
              </text>
            </view>
          </view>
        </view>
    
        <!-- 录音中的进度圆环 -->
         	<view class="record-btn-con">
               <view class="progress-circle" v-if="item.recording" :style="{ 'animation-duration': getRecordDuration(index) + 's' }"></view>
               <view class="center-circle"></view> 
                <!-- 分数显示 -->
                <view v-if="!item.recording && item.score" class="score" :style="{ color: item.scoreColor }">
                   {{ item.score }}
                </view>
              
        </view>
      </view>
    </view>

    <!-- 底部操作栏 -->
    <view class="bottom-bar">
      <view class="bottom-btn" @click="reRecord">
        <view class="btn-icon-wrapper">
          <image class="btn-icon-img" src="/static/images/record/reStart.png" mode="aspectFit"></image>
        </view>
        <text class="btn-label">重录</text>
      </view>

      <view class="bottom-btn center-btn" @click="toggleRecording">
        <view class="btn-icon-wrapper center-icon" :class="{ 'recording': isRecording }">
          <image class="btn-icon-img" src="/static/images/record/pause.png" mode="aspectFit"></image>
        </view>
        <text class="btn-label">{{ isRecording ? '录音中...' : '点击话筒开始录制' }}</text>
      </view>

      <view class="bottom-btn" @click="submit">
        <view class="btn-icon-wrapper">
          <image class="btn-icon-img" src="/static/images/record/submit.png" mode="aspectFit"></image>
        </view>
        <text class="btn-label">提交</text>
      </view>
    </view>
  </view>
</template>

<script>
import Tools from '/utils/index.js';

export default {
  data() {
    return {
      isRecording: false,
      currentSentence: 0,
      sentences: [
        // {
        //   text: 'Unit one Hello!',
        //   score: 98,
        //   textColor: '#4CAF50',
        //   scoreColor: '#4CAF50',
        //   highlighted: true,
        //   showButtons: true,
        //   recording: false
        // },

      ],
      // 音频播放相关
      audioContext: null,
      isPlaying: false,
      currentPlayingIndex: -1,
      currentPlayingType: '', // 'original' 或 'recorded'
      audioDuration: 0, // 音频总时长
      recordingTimer: null, // 录音定时器
      isManualStop: false, // 是否手动停止录音
      isProcessingResult: false // 是否正在处理录音结果
    }
  },
  computed: {
    // 检查是否所有句子都已录音完成
    allSentencesRecorded() {
      return this.sentences.length > 0 && this.sentences.every(sentence =>
        sentence.score !== '' && sentence.score !== null && sentence.score !== undefined
      )
    }
  },
  onLoad() {
    this.handData()
    this.initAudio()

    // 为iOS设置全局回调函数
    if (/(iPhone|iPad|iPod|iOS)/i.test(navigator.userAgent)) {
      window.handleRecordingResult = (score) => {
        this.handleRecordingResult(score)
      }
    }
  },
  onUnload() {
    // 页面卸载时清理音频资源
    this.stopAudio()
    // 清理录音定时器
    if (this.recordingTimer) {
      clearTimeout(this.recordingTimer)
      this.recordingTimer = null
    }
  },
  methods: {
    handData() {
        const data = Tools.getStorage('recordCompare');
        const list = data.content.split('\n');
        console.log(list);
        list.forEach((item,index)=>{
          if(item) {
   const itemArr = item.split('   ');
        this.sentences.push({
                text: itemArr[1],
                cnText: itemArr[2],
                 time: this.getTime(itemArr[0]),
                score: '',
                textColor: '#999999', // 初始为灰色，录音后根据分数变色
                scoreColor: '#4CAF50',
                highlighted: false,
                showButtons: false,
                recording: false,
                audioUrl: data.audio_url,
                userAudio: '',
                id: index
          })

          }
         
        })
     console.log('处理后的句子数据:', this.sentences);

     // 预加载音频以获取时长
     this.preloadAudio();

     // 初始化第一句为选中状态
     if (this.sentences.length > 0) {
       this.sentences[0].highlighted = true
       this.sentences[0].showButtons = true
       this.currentSentence = 0
     }

    },
    getTime(timeStr) {
      const match = timeStr.match(/\[(\d{2}):(\d{2})\.(\d{2})\]/);
      if (match) {
        const minutes = parseInt(match[1], 10);
        const seconds = parseInt(match[2], 10);
        const milliseconds = parseInt(match[3], 10);

        const totalSeconds = minutes * 60 + seconds + milliseconds / 100;
        return totalSeconds
      }
    },
    goBack() {
      uni.navigateBack()
    },
    selectSentence(index) {
      // 检查是否可以手动切换句子（所有句子都已录音完成）
      if (!this.canSwitchSentence()) {
        uni.showToast({
          title: '请先完成所有句子的录音',
          icon: 'none'
        })
        return
      }

      // 重置所有句子状态
      this.sentences.forEach((item, i) => {
        item.highlighted = i === index
        item.showButtons = i === index
      })
      this.currentSentence = index
    },

    // 检查是否可以切换句子（仅用于手动切换）
    canSwitchSentence() {
      // 检查是否所有句子都已录音完成
      return this.sentences.every(sentence => sentence.score !== '' && sentence.score !== null && sentence.score !== undefined)
    },

    playOriginal(index) {
      console.log('播放原音:', this.sentences[index].text)

      // 如果正在播放同一个原音，则停止播放
      if (this.isPlaying && this.currentPlayingIndex === index && this.currentPlayingType === 'original') {
        this.stopAudio()
        return
      }

      const item = this.sentences[index]
      if (!item.audioUrl) {
        uni.showToast({
          title: '暂无音频',
          icon: 'none'
        })
        return
      }
      this.playAudio(index, 'original')
    },
    playRecorded(index) {
      console.log('播放录音:', this.sentences[index].text)

      // 如果正在播放同一个录音，则停止播放
      if (this.isPlaying && this.currentPlayingIndex === index && this.currentPlayingType === 'recorded') {
        this.stopAudio()
        return
      }

      const item = this.sentences[index]
      if (!item.userAudio) {
        uni.showToast({
          title: '暂无录音',
          icon: 'none'
        })
        return
      }
      Tools.playRecording(item.id)
      // this.playAudio( index, 'recorded')
    },
    // 原生录音相关方法
    startNativeRecording(data) {


      try {
        if (/(Android)/i.test(navigator.userAgent)) {
          // Android录音
       
           callNative.startRecordAudio(data.id)
        } else if (/(iPhone|iPad|iPod|iOS)/i.test(navigator.userAgent)) {
          // iOS录音
          console.log('启动iOS录音')
          window.webkit.messageHandlers.startRecordAudio.postMessage(data.id)
        }
        Tools.startBs(data.text);
      } catch (error) {
        console.error('启动录音失败:', error)
        uni.showToast({
          title: '录音启动失败',
          icon: 'none'
        })
      }
    },
    	//  调js告知实时评测数据
		getRecordData(res) {
			console.log(res, 'getRecordData')
			this.recordData = res;
		},
		//  Android背诵完成调js告知总分数等数据
		overRecord(res) {
			this.$nextTick(() => {
			     this.handleRecordingResult(res)

			});

			console.log('overRecord方法执行', res);
		},

    stopNativeRecording() {
      try {
        // 清除录音定时器
        if (this.recordingTimer) {
          clearTimeout(this.recordingTimer)
          this.recordingTimer = null
        }

        this.isRecording = false
        this.sentences[this.currentSentence].recording = false

        if (/(Android)/i.test(navigator.userAgent)) {
          // Android停止录音并获取分数
          console.log('停止Android录音并获取分数')
          const score = callNative.stopRecording()
          // Tools.bsCompletedAndResult();
          // 延迟处理结果，避免与手动停止冲突
          setTimeout(() => {
            this.handleRecordingResult(score || 60)
          }, 100)
        } else if (/(iPhone|iPad|iPod|iOS)/i.test(navigator.userAgent)) {
          // iOS停止录音
          console.log('停止iOS录音')
          window.webkit.messageHandlers.stopRecording.postMessage(null)
          // Tools.bsCompletedAndResult();
          // iOS的结果会通过回调函数返回，这里延迟处理默认分数
          setTimeout(() => {
            this.handleRecordingResult(60)
          }, 100)
        }
      } catch (error) {
        console.error('停止录音失败:', error)
        // 清除录音定时器
        if (this.recordingTimer) {
          clearTimeout(this.recordingTimer)
          this.recordingTimer = null
        }
        this.isRecording = false
        this.sentences[this.currentSentence].recording = false
        uni.showToast({
          title: '录音停止失败',
          icon: 'none'
        })
      }
    },

    // 处理录音结果
    handleRecordingResult(score) {
      // 防止重复处理
      if (this.isProcessingResult) {
        console.log('正在处理录音结果，忽略重复调用')
        return
      }

      this.isProcessingResult = true
      console.log('录音完成，分数:', score)
      console.log('当前句子索引:', this.currentSentence)

      // 设置当前句子的分数和状态
      this.sentences[this.currentSentence].score = score
      this.sentences[this.currentSentence].userAudio = 'recorded'

      console.log('设置后的句子数据:', this.sentences[this.currentSentence])

      // 根据分数设置颜色
      if (score >= 80) {
        this.sentences[this.currentSentence].scoreColor = '#4CAF50'
        this.sentences[this.currentSentence].textColor = '#4CAF50'
      } else if (score >= 60) {
        this.sentences[this.currentSentence].scoreColor = '#FF9800'
        this.sentences[this.currentSentence].textColor = '#FF9800'
      } else {
        this.sentences[this.currentSentence].scoreColor = '#F44336'
        this.sentences[this.currentSentence].textColor = '#F44336'
      }

      // 自动跳到下一句
      this.goToNextSentence()

      // 重置处理标志
      this.isProcessingResult = false
    },

    // 跳到下一句
    goToNextSentence() {
      // 如果不是最后一句，自动跳到下一句
      if (this.currentSentence < this.sentences.length - 1) {
        setTimeout(() => {
          const nextIndex = this.currentSentence + 1
          // 直接设置下一句为当前句子，不通过selectSentence以避免权限检查
          this.sentences.forEach((item, i) => {
            item.highlighted = i === nextIndex
            item.showButtons = i === nextIndex
          })
          this.currentSentence = nextIndex
          console.log('自动跳到下一句:', this.sentences[nextIndex].text)

          // 只有在非手动停止的情况下才自动开始录音下一句
          if (!this.isManualStop) {
            setTimeout(() => {
              this.autoStartRecording()
            }, 1000) // 延迟1秒后自动开始录音
          } else {
            // 重置手动停止标志
            this.isManualStop = false
            console.log('手动停止录音，不自动开始下一句录音')
          }
        }, 500) // 延迟500ms让用户看到分数
      } else {
        // 如果是最后一句，显示完成提示
        setTimeout(() => {
          uni.showToast({
            title: '录音完成！',
            icon: 'success'
          })
        }, 500)
      }
    },

    // 自动开始录音
    autoStartRecording() {
      if (!this.isRecording && !this.isProcessingResult) {
        this.toggleRecording()
      } else {
        console.log('无法自动开始录音：正在录音或处理结果中')
      }
    },
    // {
    //   "DeleteAllRecordings": function(), 
    //   "closePage": function(), 
    //   "playRecording": function(),
    //    "startRecordAudio": function(), 
    //    "stopPlaying": function(), 
    //    "stopRecording": function()} Android
    toggleRecording() {
      console.log(callNative, 'Android')

      	// if(Tools.isPermissionsRecordAudioMethod() !=1) {
				// 	Tools.permissionsRecordAudioMethod();
				// 	return
				// }

      // 如果正在处理录音结果，忽略点击
      if (this.isProcessingResult) {
        console.log('正在处理录音结果，忽略点击')
        return
      }

      if (this.isRecording) {
        // 如果正在录音，立即停止录音（手动停止）
        this.isManualStop = true
        // 清除定时器防止重复触发
        if (this.recordingTimer) {
          clearTimeout(this.recordingTimer)
          this.recordingTimer = null
        }
        this.stopNativeRecording()
      } else {
        // 开始录音前先停止音频播放
        if (this.isPlaying) {
          this.stopAudio()
        }

        // 开始录音
        this.isRecording = true
        this.isManualStop = false // 重置手动停止标志
        this.sentences[this.currentSentence].recording = true

        // 调用原生录音接口
        this.startNativeRecording(this.sentences[this.currentSentence])

        // 获取当前句子的录音时长（和播放原音时间一样）
        const recordDuration = this.getRecordDuration(this.currentSentence)

        // 录音过程，使用实际的时长
        this.recordingTimer = setTimeout(() => {
          // 检查是否仍在录音状态，防止重复触发
          if (this.isRecording && this.recordingTimer) {
            // 自动停止录音，不设置手动停止标志
            this.isManualStop = false
            this.recordingTimer = null
            this.stopNativeRecording()
          }
        }, recordDuration * 1000)
      }
    },
    reRecord() {
      console.log('重新录音')
      // 实现重录逻辑
    },
    submit() {
      console.log('提交录音')
      // 实现提交逻辑
    },

    // 音频播放相关方法
    initAudio() {
      this.audioContext = uni.createInnerAudioContext()

      // 监听音频播放结束
      this.audioContext.onEnded(() => {
        this.isPlaying = false
        this.currentPlayingIndex = -1
        this.currentPlayingType = ''
      })

      // 监听音频播放错误
      this.audioContext.onError((res) => {
        console.error('音频播放错误:', res)
        this.isPlaying = false
        this.currentPlayingIndex = -1
        this.currentPlayingType = ''
        uni.showToast({
          title: '播放失败',
          icon: 'none'
        })
      })

      // 监听音频播放开始
      this.audioContext.onPlay(() => {
        this.isPlaying = true
      })

      // 监听音频暂停
      this.audioContext.onPause(() => {
        this.isPlaying = false
      })

      // 监听音频可以播放时获取时长
      this.audioContext.onCanplay(() => {
        if (this.audioContext.duration && this.audioContext.duration > 0) {
          this.audioDuration = this.audioContext.duration
          console.log('音频总时长:', this.audioDuration + '秒')
        }
      })
    },

    playAudio(index, type) {
      // 如果正在播放，先停止
      if (this.isPlaying) {
        this.stopAudio()
      }

      const currentItem = this.sentences[index]
      const startTime = Number(currentItem.time)

      // 根据播放类型获取对应的音频URL
      const audioUrl = type === 'original' ? currentItem.audioUrl : currentItem.userAudio

      // 计算结束时间（下一句的开始时间）
      let endTime = null
      if (index < this.sentences.length - 1) {
        endTime = Number(this.sentences[index + 1].time)
      }

      console.log(`播放${type === 'original' ? '原音' : '录音'}:`, {
        audioUrl,
        startTime,
        endTime,
        text: currentItem.text
      })

      this.currentPlayingIndex = index
      this.currentPlayingType = type

      // 设置音频源
      this.audioContext.src = audioUrl;
      this.audioContext.seek(startTime)
			this.audioContext.startTime = Number(startTime);

        console.log(4)


      // 监听音频可以播放时跳转到指定时间
      // this.audioContext.onCanplay(() => {
        console.log(1, startTime)
        if (startTime) {
        console.log(2)

          this.audioContext.seek(startTime)
        }
        console.log(3)

        this.audioContext.play()

        // 如果有结束时间，设置定时器在指定时间停止播放
        if (endTime && endTime > startTime) {
          const playDuration = (endTime - startTime) * 1000 // 转换为毫秒
          setTimeout(() => {
            if (this.isPlaying && this.currentPlayingIndex === index) {
              this.stopAudio()
            }
          }, playDuration)
        }
      // })

      // 如果没有开始时间，直接播放
      if (!startTime) {
        this.audioContext.play()
      }
    },

    stopAudio() {
      if (this.audioContext) {
        this.audioContext.stop()
        this.isPlaying = false
        this.currentPlayingIndex = -1
        this.currentPlayingType = ''
      }
    },

    // 获取录音时长（和播放原音时间一样）
    getRecordDuration(index) {
      const currentItem = this.sentences[index]
      const startTime = Number(currentItem.time)

      // 计算结束时间（下一句的开始时间）
      let endTime = null
      if (index < this.sentences.length - 1) {
        endTime = Number(this.sentences[index + 1].time)
      } else {
        // 如果是最后一句，时间等于音频时长减去startTime
        // 需要先获取音频时长，这里暂时使用一个默认值，实际应该从音频文件获取
        const audioDuration = this.getAudioDuration() // 获取音频总时长
        endTime = audioDuration
      }

      const duration = endTime - startTime;
      return duration > 0 ? duration : 3 // 最少3秒
    },

    // 获取音频总时长
    getAudioDuration() {
      // 如果已经获取到音频时长，直接返回
      if (this.audioDuration > 0) {
        return this.audioDuration
      }

      // 如果还没有获取到，尝试从当前音频上下文获取
      if (this.audioContext && this.audioContext.duration > 0) {
        this.audioDuration = this.audioContext.duration
        return this.audioDuration
      }

      // 如果都没有，返回一个默认值
      return 60 // 默认60秒
    },

    // 预加载音频以获取时长
    preloadAudio() {
      if (this.sentences.length > 0 && this.sentences[0].audioUrl) {
        // 创建一个临时的音频上下文来获取时长
        const tempAudio = uni.createInnerAudioContext()
        tempAudio.src = this.sentences[0].audioUrl

        tempAudio.onCanplay(() => {
          if (tempAudio.duration && tempAudio.duration > 0) {
            this.audioDuration = tempAudio.duration
            console.log('预加载获取音频总时长:', this.audioDuration + '秒')
          }
          // 销毁临时音频对象
          tempAudio.destroy()
        })

        tempAudio.onError(() => {
          console.error('预加载音频失败')
          tempAudio.destroy()
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
 @property --p {
	syntax: '<percentage>';
	inherits: false;
	initial-value: 0%;
}
  
.page-container {
  min-height: 100vh;
  padding-bottom: 170rpx;
}


.sentence-list {
  padding: 20rpx;
}

.sentence-card {
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  padding: 30rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  border: 2rpx solid #DFDFDF;
  
  
  &.highlighted {
    border: 2rpx solid #FFB300;
  }

  &.recording {
    border: 2rpx solid #FF5722;
    background-color: #FFF3E0;
  }
  
  .sentence-content {
    flex: 1;
    
    .sentence-text {
      font-size: 32rpx;
      line-height: 1.4;
      margin-bottom: 20rpx;
    }
    
    .button-group {
      display: flex;
      gap: 20rpx;
      
      .record-btn {
        display: flex;
        align-items: center;
        padding: 12rpx 24rpx;
        border-radius: 40rpx;
        
        &.original {
          background-color: #FFB300;

          &.playing {
            background-color: #FF5722;
            animation: pulse 1.5s infinite;
          }
        }

        &.recorded {
          background-color: #FFB300;

          &.playing {
            background-color: #FF5722;
            animation: pulse 1.5s infinite;
          }
        }
        
        .btn-icon {
          font-size: 24rpx;
          margin-right: 8rpx;
        }
        
        .btn-text {
          font-size: 24rpx;
          color: #fff;
        }
      }
    }

  }
  
  .score {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 24rpx;
    font-weight: bold;
    z-index: 3;
    text-align: center;
    line-height: 1;
  }

  .record-btn-con {
		width: 80upx;
		height:  80upx;
		border-radius: 50%;
		overflow: hidden;
	  position: absolute;
    right: 30upx;
    top: 50%;
    transform: translateY(-50%);
		// background-color: rgba(0, 0, 0, 0.6);
		// 新增圆形进度条样式
		.progress-circle {
			--p: 0%;
			position: absolute;
			width: 100%;
			height: 100%;
			top: 0;
			left: 0;
			border-radius: 50%;
			background: conic-gradient(#ed9126 0, #ed9126 var(--p), #ddd var(--p));
			animation: erase 2s linear forwards;
			pointer-events: none;
			z-index: 1;
		}
    .center-circle {
			width: 65upx;
			height: 65upx;
			position: absolute;
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%);
			z-index: 2;
			pointer-events: auto;
      background-color: #fff;
			border-radius: 50%;
			display: flex;
			align-items: center;
			justify-content: center;
			// box-shadow: 0 2upx 8upx rgba(0, 0, 0, 0.1);
		}
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.7; }
  100% { opacity: 1; }
}

@keyframes erase {
	0% {
		--p: 0%;
	}
	100% {
		--p: 100%;
	}
}

.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 30rpx;
  display: flex;
  align-items: center;
  justify-content: space-around;
  border-top: 1rpx solid #eee;
  
  .bottom-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    
    .btn-icon-wrapper {
      width: 80rpx;
      height: 80rpx;
      border-radius: 50%;
    //   background-color: #f0f0f0;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 10rpx;

      &.center-icon {
        width: 100rpx;
        height: 100rpx;
        background-color: #FFB300;

        &.recording {
          background-color: #FF5722;
        }
      }

      .btn-icon {
        font-size: 36rpx;
        color: #666;
      }

      .btn-icon-img {
        width: 60rpx;
        height:60rpx;
      }
    }

    &.center-btn .btn-icon-wrapper {
      .btn-icon {
        color: #fff;
        font-size: 40rpx;
      }

      .btn-icon-img {
        width: 100rpx;
        height: 100rpx;
      }
    }
    
    .btn-label {
      font-size: 24rpx;
      color: #666;
      text-align: center;
    }
  }
}
</style>